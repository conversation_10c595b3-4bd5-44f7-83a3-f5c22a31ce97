// Globaler API-<PERSON><PERSON>hler für db-vendo-client Anfragen
let apiCallCount = 0;

// Rate-Limit-Verfolgung
let requestTimes = [];
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 Minute in Millisekunden
const MAX_REQUESTS_PER_MINUTE = 60; // DB API Limit

// Funktion zum Erhöhen des API-Zählers
export function incrementApiCount(endpoint, description = "") {
	apiCallCount++;

	// Anfragzeit für Rate-Limiting verfolgen
	const now = Date.now();
	requestTimes.push(now);

	// Alte Anfragen außerhalb des Zeitfensters entfernen
	requestTimes = requestTimes.filter((time) => now - time < RATE_LIMIT_WINDOW);

	const logMessage = description
		? `🚆 API Call #${apiCallCount} - ${endpoint}: ${description}`
		: `🚆 API Call #${apiCallCount} - ${endpoint}`;

	console.log(`\n===============================`);
	console.log(logMessage);
	console.log(`Total API calls so far: ${apiCallCount}`);
	console.log(
		`Requests in last minute: ${requestTimes.length}/${MAX_REQUESTS_PER_MINUTE}`
	);

	// Warnung wenn Rate-Limit sich nähert
	if (requestTimes.length >= MAX_REQUESTS_PER_MINUTE * 0.8) {
		console.log(
			`⚠️  WARNING: Approaching rate limit! (${requestTimes.length}/${MAX_REQUESTS_PER_MINUTE})`
		);
	}
	if (requestTimes.length >= MAX_REQUESTS_PER_MINUTE) {
		console.log(
			`🚨 RATE LIMIT REACHED! Consider adding delays between requests.`
		);
	}

	console.log(`===============================\n`);

	return apiCallCount;
}

// Funktion zum Abrufen der aktuellen API-Anzahl
export function getApiCount() {
	return apiCallCount;
}

// Funktion zum Zurücksetzen des API-Zählers
export function resetApiCount() {
	apiCallCount = 0;
	requestTimes = [];
	console.log(`\n🔄 API Counter reset to 0\n`);
}
